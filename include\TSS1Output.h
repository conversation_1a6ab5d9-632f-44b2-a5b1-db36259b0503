#ifndef TSS1Output_h
#define TSS1Output_h

/*
  TSS1 Data Format Output
  
  TSS1数据格式说明：
  格式示例：":00000A -0002F 0014 -0027"
  - 以":"开始，以空格分割，以回车换行结尾
  - 字段说明：
    1. 00 - 水平加速度（如果未使用为0）
    2. 000A - 垂直加速度（如果未使用为0）  
    3. -0002F - Heave（升沉值）+ 状态标志
    4. 0014 - 横滚 (-99.99~99.99)
    5. -0027 - 俯仰 (-99.99~99.99)
  
  状态标志含义：
  - U = 无辅助模式-已稳定
  - u = 无辅助模式-未稳定，无效数据
  - G = GPS辅助模式-稳定状态
  - g = GPS辅助模式-未稳定，无效数据
  - H = 航向辅助模式-稳定
  - h = 航向辅助模式-未稳定，无效数据
  - F = 全辅助模式-已稳定
  - f = 全辅助模式-未稳定，无效数据
*/

/**
 * 生成TSS1格式数据输出
 * @param horizontal_accel 水平加速度 (m/s²)
 * @param vertical_accel 垂直加速度 (m/s²)
 * @param heave 升沉值 (m)
 * @param roll 横滚角度 (度)
 * @param pitch 俯仰角度 (度)
 * @param is_stable 数据是否稳定
 */
void gen_tss1_data(float horizontal_accel, float vertical_accel, float heave, 
                   float roll, float pitch, bool is_stable) {
  
  // 转换数据为TSS1格式所需的整数值
  int h_accel = (int)(horizontal_accel * 1000);  // 转换为毫g单位
  int v_accel = (int)(vertical_accel * 1000);    // 转换为毫g单位
  int heave_cm = (int)(heave * 100);             // 转换为厘米
  int roll_hundredths = (int)(roll * 100);       // 保留两位小数
  int pitch_hundredths = (int)(pitch * 100);     // 保留两位小数
  
  // 限制数值范围
  h_accel = constrain(h_accel, -99999, 99999);
  v_accel = constrain(v_accel, -99999, 99999);
  heave_cm = constrain(heave_cm, -99999, 99999);
  roll_hundredths = constrain(roll_hundredths, -9999, 9999);
  pitch_hundredths = constrain(pitch_hundredths, -9999, 9999);
  
  // 确定状态标志（使用全辅助模式）
  char status = is_stable ? 'F' : 'f';
  
  // 生成TSS1格式字符串
  // 格式：":00000A -0002F 0014 -0027"
  // 分析示例格式：水平加速度(2位) + 垂直加速度(4位) + 空格 + Heave(4位) + 状态(1位) + 空格 + 横滚(4位) + 空格 + 俯仰(4位)
  Serial.printf(":%02d%04d %+05d%c %+05d %+05d\r\n",
                abs(h_accel) % 100,           // 水平加速度（2位）
                abs(v_accel) % 10000,         // 垂直加速度（4位）
                heave_cm,                     // Heave值（带符号，5位）
                status,                       // 状态标志
                roll_hundredths,              // 横滚（带符号，5位）
                pitch_hundredths);            // 俯仰（带符号，5位）
}

/**
 * 简化版TSS1输出函数，自动判断稳定性
 * @param vertical_accel 垂直加速度 (m/s²)
 * @param heave 升沉值 (m)
 * @param roll 横滚角度 (度)
 * @param pitch 俯仰角度 (度)
 * @param wave_height 波高 (m)
 */
void gen_tss1_simple(float vertical_accel, float heave, float roll, float pitch, float wave_height) {
  // 简单的稳定性判断逻辑
  bool is_stable = (wave_height < 30.0) && (fabs(heave) < 15.0);
  
  // 水平加速度暂时设为0
  float horizontal_accel = 0.0;
  
  gen_tss1_data(horizontal_accel, vertical_accel, heave, roll, pitch, is_stable);
}

#endif
